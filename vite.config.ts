import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { fileURLToPath } from "url";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";

// Get current file's directory in ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  plugins: [
    react(),
    // Disable Replit plugins that might interfere with HMR
    ...(process.env.NODE_ENV !== "production" && !process.env.REPL_ID
      ? [runtimeErrorOverlay()]
      : []),
    // Temporarily disabled due to ESM/CJS interop issues with @babel/traverse
    // ...(process.env.NODE_ENV !== "production" &&
    // process.env.REPL_ID !== undefined
    //   ? [
    //       await import("@replit/vite-plugin-cartographer").then((m) =>
    //         m.cartographer(),
    //       ),
    //     ]
    //   : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "client", "src"),
      "@shared": path.resolve(__dirname, "shared"),
      "@assets": path.resolve(__dirname, "attached_assets"),
    },
  },
  root: path.resolve(__dirname, "client"),
  build: {
    outDir: path.resolve(__dirname, "dist/public"),
    emptyOutDir: true,
    // Production optimizations
    minify: 'esbuild',
    sourcemap: process.env.NODE_ENV !== 'production',
    rollupOptions: {
      output: {
        // Optimize chunk splitting for better caching
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          router: ['wouter'],
          query: ['@tanstack/react-query'],
        },
        // Add hash to filenames for cache busting
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    // Increase chunk size warning limit for production
    chunkSizeWarningLimit: 1000,
  },
  server: {
    host: "0.0.0.0", // Allow external connections in Docker
    port: 3000,
    proxy: {
      "/api": {
        target: "http://localhost:5000",
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
    // Add the allowedHosts configuration here
    allowedHosts: [
      "304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev",
      "chewy-ai.replit.app",
      // It's good practice to also keep localhost and 127.0.0.1 if you use them directly
      "localhost",
      "127.0.0.1",
    ],
    // Handle port conflicts in Replit
    strictPort: false,  // Allow Vite to use alternative ports if 3000 is busy
    // Fix HMR WebSocket connection issues
    hmr: process.env.REPL_ID ? false : {
      // Local development environment only
      port: 24679,
      host: "localhost",
    },
    // Additional configuration for Replit
    ...(process.env.REPL_ID && {
      // Disable watch mode in Replit to prevent file system issues
      watch: null,
    }),
  },
});
