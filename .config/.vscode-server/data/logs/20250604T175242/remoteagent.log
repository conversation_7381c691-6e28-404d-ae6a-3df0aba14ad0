2025-06-04 17:52:42.962 [info] 




2025-06-04 17:52:42.973 [info] Extension host agent started.
2025-06-04 17:52:43.164 [info] [<unknown>][947f3152][ExtensionHostConnection] New connection established.
2025-06-04 17:52:43.165 [info] [<unknown>][07ee4c58][ManagementConnection] New connection established.
2025-06-04 17:52:43.173 [info] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2
2025-06-04 17:52:43.174 [info] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.328.1602
2025-06-04 17:52:43.249 [info] [<unknown>][947f3152][ExtensionHostConnection] <7298> Launched Extension Host Process.
2025-06-04 17:52:43.474 [info] ComputeTargetPlatform: linux-x64
2025-06-04 17:52:46.552 [info] ComputeTargetPlatform: linux-x64
2025-06-04 17:57:42.974 [info] New EH opened, aborting shutdown
2025-06-04 18:24:33.232 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:24:33.439 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:26:20.974 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:26:28.217 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:26:35.921 [error] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:26:35.935 [error] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:26:36.180 [error] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-04 18:26:40.404 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
